// Project Analytics component for Project Analysis Dashboard
export class ProjectAnalytics {
  constructor(container, parentComponent) {
    this.container = container;
    this.parentComponent = parentComponent;
    this.chart = null;
  }

  async init() {
    await this.loadApexCharts();
    this.render();
  }

  async loadApexCharts() {
    return new Promise((resolve, reject) => {
      if (window.Chart) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = '/kpi/library/chart.min.js';
      script.onload = () => {
        console.log('Chart.js loaded successfully');
        resolve();
      };
      script.onerror = () => {
        console.error('Failed to load Chart.js');
        reject(new Error('Failed to load Chart.js'));
      };
      document.head.appendChild(script);
    });
  }

  generateWeeksOfYear(year = new Date().getFullYear()) {
    const weeks = [];
    
    // Start from January 6th as the first week
    const startDate = new Date(year, 0, 6); // January 6th
    
    // Continue until we reach the end of the year
    let currentDate = new Date(startDate);
    
    while (currentDate.getFullYear() === year || 
           (currentDate.getFullYear() === year + 1 && currentDate.getMonth() === 0 && currentDate.getDate() <= 2)) {
      
      const weekStart = new Date(currentDate);
      const weekEnd = new Date(currentDate);
      weekEnd.setDate(weekEnd.getDate() + 4); // Add 4 days to get Friday (5-day week)
      
      // Format the week label
      const startMonth = weekStart.toLocaleDateString('en-US', { month: 'short' });
      const endMonth = weekEnd.toLocaleDateString('en-US', { month: 'short' });
      const startDay = weekStart.getDate();
      const endDay = weekEnd.getDate();
      
      let weekLabel;
      if (startMonth === endMonth) {
        weekLabel = `${startMonth} ${startDay}-${endDay}`;
      } else {
        weekLabel = `${startMonth} ${startDay}-${endMonth} ${endDay}`;
      }
      
      weeks.push({
        label: weekLabel,
        startDate: new Date(weekStart),
        endDate: new Date(weekEnd),
        completedUnits: 0
      });
      
      // Move to next week (skip weekends, go to next Monday)
      currentDate.setDate(currentDate.getDate() + 7);
    }
    
    return weeks;
  }

  calculateWeeklyCompletions() {
    const weeks = this.generateWeeksOfYear();
    const projects = this.parentComponent.projects || [];
    
    // Count completed units for each week
    weeks.forEach(week => {
      let completedCount = 0;
      
      projects.forEach(project => {
        if (project.Subitems && Array.isArray(project.Subitems)) {
          project.Subitems.forEach(subitem => {
            if (subitem.completionDate && subitem.completionDate instanceof Date) {
              const completionDate = subitem.completionDate;
              
              // Check if completion date falls within this week
              if (completionDate >= week.startDate && completionDate <= week.endDate) {
                completedCount++;
              }
            }
          });
        }
      });
      
      week.completedUnits = completedCount;
    });
    
    return weeks;
  }

  render() {
    if (!this.container) return;

    this.container.innerHTML = `
      <div class="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">
        <div class="mb-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-2">Weekly Units Completed</h2>
          <p class="text-gray-600 dark:text-gray-400">Number of units completed each week based on completion dates</p>
        </div>
        <div class="w-full" style="height: 400px;">
          <canvas id="weekly-completion-chart" style="width: 100%; height: 100%;"></canvas>
        </div>
      </div>
    `;

    this.renderWeeklyChart();
  }

  renderWeeklyChart() {
    const weeklyData = this.calculateWeeklyCompletions();
    const ctx = document.getElementById('weekly-completion-chart').getContext('2d');

    if (this.chart) {
      this.chart.destroy();
    }

    this.chart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: weeklyData.map(week => week.label),
        datasets: [{
          label: '# of Units Completed',
          data: weeklyData.map(week => week.completedUnits),
          borderColor: '#3B82F6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderWidth: 3,
          pointRadius: 5,
          pointBackgroundColor: '#3B82F6',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointHoverRadius: 7,
          fill: true,
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: true,
            position: 'top'
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                return context.parsed.y + ' units';
              }
            }
          }
        },
        scales: {
          x: {
            title: {
              display: true,
              text: 'Weeks',
              color: '#374151',
              font: {
                size: 14,
                weight: 600
              }
            },
            ticks: {
              maxRotation: 45,
              color: '#6B7280',
              font: {
                size: 12
              }
            },
            grid: {
              color: '#E5E7EB',
              borderDash: [3, 3]
            }
          },
          y: {
            title: {
              display: true,
              text: '# of Units Completed',
              color: '#374151',
              font: {
                size: 14,
                weight: 600
              }
            },
            ticks: {
              color: '#6B7280',
              beginAtZero: true
            },
            grid: {
              color: '#E5E7EB',
              borderDash: [3, 3]
            }
          }
        },
        interaction: {
          intersect: false,
          mode: 'index'
        }
      }
    });
  }

  async updateData() {
    if (this.chart) {
      const weeklyData = this.calculateWeeklyCompletions();

      this.chart.data.labels = weeklyData.map(week => week.label);
      this.chart.data.datasets[0].data = weeklyData.map(week => week.completedUnits);
      this.chart.update();
    }
  }
} 