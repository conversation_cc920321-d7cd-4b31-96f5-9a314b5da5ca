// Project Analytics component for Project Analysis Dashboard
export class ProjectAnalytics {
  constructor(container, parentComponent) {
    this.container = container;
    this.parentComponent = parentComponent;
    this.chart = null;
  }

  async init() {
    await this.loadApexCharts();
    this.render();
  }

  async loadApexCharts() {
    return new Promise((resolve, reject) => {
      if (window.ApexCharts) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = '/kpi/library/apexcharts.min.js';
      script.onload = () => {
        console.log('ApexCharts loaded successfully');
        resolve();
      };
      script.onerror = () => {
        console.error('Failed to load ApexCharts');
        reject(new Error('Failed to load ApexCharts'));
      };
      document.head.appendChild(script);
    });
  }

  generateWeeksOfYear(year = new Date().getFullYear()) {
    const weeks = [];
    
    // Start from January 6th as the first week
    const startDate = new Date(year, 0, 6); // January 6th
    
    // Continue until we reach the end of the year
    let currentDate = new Date(startDate);
    
    while (currentDate.getFullYear() === year || 
           (currentDate.getFullYear() === year + 1 && currentDate.getMonth() === 0 && currentDate.getDate() <= 2)) {
      
      const weekStart = new Date(currentDate);
      const weekEnd = new Date(currentDate);
      weekEnd.setDate(weekEnd.getDate() + 4); // Add 4 days to get Friday (5-day week)
      
      // Format the week label
      const startMonth = weekStart.toLocaleDateString('en-US', { month: 'short' });
      const endMonth = weekEnd.toLocaleDateString('en-US', { month: 'short' });
      const startDay = weekStart.getDate();
      const endDay = weekEnd.getDate();
      
      let weekLabel;
      if (startMonth === endMonth) {
        weekLabel = `${startMonth} ${startDay}-${endDay}`;
      } else {
        weekLabel = `${startMonth} ${startDay}-${endMonth} ${endDay}`;
      }
      
      weeks.push({
        label: weekLabel,
        startDate: new Date(weekStart),
        endDate: new Date(weekEnd),
        completedUnits: 0
      });
      
      // Move to next week (skip weekends, go to next Monday)
      currentDate.setDate(currentDate.getDate() + 7);
    }
    
    return weeks;
  }

  calculateWeeklyCompletions() {
    const weeks = this.generateWeeksOfYear();
    const projects = this.parentComponent.projects || [];
    
    // Count completed units for each week
    weeks.forEach(week => {
      let completedCount = 0;
      
      projects.forEach(project => {
        if (project.Subitems && Array.isArray(project.Subitems)) {
          project.Subitems.forEach(subitem => {
            if (subitem.completionDate && subitem.completionDate instanceof Date) {
              const completionDate = subitem.completionDate;
              
              // Check if completion date falls within this week
              if (completionDate >= week.startDate && completionDate <= week.endDate) {
                completedCount++;
              }
            }
          });
        }
      });
      
      week.completedUnits = completedCount;
    });
    
    return weeks;
  }

  render() {
    if (!this.container) return;

    this.container.innerHTML = `
      <div class="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">
        <div class="mb-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-2">Weekly Units Completed</h2>
          <p class="text-gray-600 dark:text-gray-400">Number of units completed each week based on completion dates</p>
        </div>
        <div id="weekly-completion-chart" class="w-full" style="height: 400px;"></div>
      </div>
    `;

    this.renderWeeklyChart();
  }

  renderWeeklyChart() {
    const weeklyData = this.calculateWeeklyCompletions();
    
    const chartOptions = {
      series: [{
        name: '# of Units Completed',
        data: weeklyData.map(week => week.completedUnits)
      }],
      chart: {
        type: 'line',
        height: 400,
        zoom: {
          enabled: true
        },
        toolbar: {
          show: true
        }
      },
             stroke: {
         curve: 'smooth',
         width: 4,
         colors: ['#1E40AF']
       },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          type: 'vertical',
          shadeIntensity: 0.1,
          gradientToColors: ['#60A5FA'],
          inverseColors: false,
          opacityFrom: 0.4,
          opacityTo: 0.1,
          stops: [0, 100]
        }
      },
      dataLabels: {
        enabled: false
      },
      grid: {
        borderColor: '#E5E7EB',
        strokeDashArray: 3,
        xaxis: {
          lines: {
            show: true
          }
        },
        yaxis: {
          lines: {
            show: true
          }
        }
      },
      xaxis: {
        categories: weeklyData.map(week => week.label),
        labels: {
          rotate: -45,
          style: {
            colors: '#6B7280',
            fontSize: '12px'
          }
        },
        title: {
          text: 'Weeks',
          style: {
            color: '#374151',
            fontSize: '14px',
            fontWeight: 600
          }
        }
      },
      yaxis: {
        title: {
          text: '# of Units Completed',
          style: {
            color: '#374151',
            fontSize: '14px',
            fontWeight: 600
          }
        },
        labels: {
          style: {
            colors: '#6B7280'
          }
        },
        min: 0
      },
      theme: {
        mode: 'light'
      },
      tooltip: {
        theme: 'light',
        x: {
          show: true
        },
        y: {
          formatter: function(value) {
            return value + ' units';
          }
        }
      },
      markers: {
        size: 4,
        colors: ['#3B82F6'],
        strokeColors: '#ffffff',
        strokeWidth: 2,
        hover: {
          size: 6
        }
      }
    };

    if (this.chart) {
      this.chart.destroy();
    }

    this.chart = new ApexCharts(document.querySelector("#weekly-completion-chart"), chartOptions);
    this.chart.render();
  }

  async updateData() {
    if (this.chart) {
      const weeklyData = this.calculateWeeklyCompletions();
      
      await this.chart.updateSeries([{
        name: '# of Units Completed',
        data: weeklyData.map(week => week.completedUnits)
      }]);
      
      await this.chart.updateOptions({
        xaxis: {
          categories: weeklyData.map(week => week.label)
        }
      });
    }
  }
} 