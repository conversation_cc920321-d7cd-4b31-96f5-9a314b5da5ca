// Project Analytics component for Project Analysis Dashboard
export class ProjectAnalytics {
  constructor(container, parentComponent) {
    this.container = container;
    this.parentComponent = parentComponent;
    this.chart = null;
    this.onTimeChart = null;
    this.lateDaysChart = null;
  }

  async init() {
    await this.loadApexCharts();
    this.render();
  }

  async loadApexCharts() {
    return new Promise((resolve, reject) => {
      if (window.echarts) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = '/kpi/library/echarts.js';
      script.onload = () => {
        console.log('ECharts loaded successfully');
        resolve();
      };
      script.onerror = () => {
        console.error('Failed to load ECharts');
        reject(new Error('Failed to load ECharts'));
      };
      document.head.appendChild(script);
    });
  }

  generateWeeksOfYear(year = new Date().getFullYear()) {
    const weeks = [];
    
    // Start from January 6th as the first week
    const startDate = new Date(year, 0, 6); // January 6th
    
    // Continue until we reach the end of the year
    let currentDate = new Date(startDate);
    
    while (currentDate.getFullYear() === year || 
           (currentDate.getFullYear() === year + 1 && currentDate.getMonth() === 0 && currentDate.getDate() <= 2)) {
      
      const weekStart = new Date(currentDate);
      const weekEnd = new Date(currentDate);
      weekEnd.setDate(weekEnd.getDate() + 4); // Add 4 days to get Friday (5-day week)
      
      // Format the week label
      const startMonth = weekStart.toLocaleDateString('en-US', { month: 'short' });
      const endMonth = weekEnd.toLocaleDateString('en-US', { month: 'short' });
      const startDay = weekStart.getDate();
      const endDay = weekEnd.getDate();
      
      let weekLabel;
      if (startMonth === endMonth) {
        weekLabel = `${startMonth} ${startDay}-${endDay}`;
      } else {
        weekLabel = `${startMonth} ${startDay}-${endMonth} ${endDay}`;
      }
      
      weeks.push({
        label: weekLabel,
        startDate: new Date(weekStart),
        endDate: new Date(weekEnd),
        completedUnits: 0
      });
      
      // Move to next week (skip weekends, go to next Monday)
      currentDate.setDate(currentDate.getDate() + 7);
    }
    
    return weeks;
  }

  calculateWeeklyCompletions() {
    const weeks = this.generateWeeksOfYear();
    const projects = this.parentComponent.projects || [];

    // Count completed units for each week
    weeks.forEach(week => {
      let completedCount = 0;

      projects.forEach(project => {
        if (project.Subitems && Array.isArray(project.Subitems)) {
          project.Subitems.forEach(subitem => {
            if (subitem.completionDate && subitem.completionDate instanceof Date) {
              const completionDate = subitem.completionDate;

              // Check if completion date falls within this week
              if (completionDate >= week.startDate && completionDate <= week.endDate) {
                completedCount++;
              }
            }
          });
        }
      });

      week.completedUnits = completedCount;
    });

    return weeks;
  }

  calculateWeeklyOnTimePerformance() {
    const weeks = this.generateWeeksOfYear();
    const projects = this.parentComponent.projects || [];

    // Calculate on-time performance for each week
    weeks.forEach(week => {
      let totalCompleted = 0;
      let onTimeCompleted = 0;

      projects.forEach(project => {
        if (project.Subitems && Array.isArray(project.Subitems)) {
          project.Subitems.forEach(subitem => {
            if (subitem.completionDate && subitem.completionDate instanceof Date) {
              const completionDate = subitem.completionDate;

              // Check if completion date falls within this week
              if (completionDate >= week.startDate && completionDate <= week.endDate) {
                totalCompleted++;

                // Check if completed on time (completion date <= due date)
                if (subitem.productionDueDate && subitem.productionDueDate instanceof Date) {
                  if (completionDate <= subitem.productionDueDate) {
                    onTimeCompleted++;
                  }
                }
              }
            }
          });
        }
      });

      // Calculate percentage (0-100)
      week.onTimePercentage = totalCompleted > 0 ? Math.round((onTimeCompleted / totalCompleted) * 100) : 0;
      week.totalCompleted = totalCompleted;
      week.onTimeCompleted = onTimeCompleted;
    });

    return weeks;
  }

  calculateWeeklyAverageLateDays() {
    const weeks = this.generateWeeksOfYear();
    const projects = this.parentComponent.projects || [];

    // Calculate average late days for each week
    weeks.forEach(week => {
      let totalLateDays = 0;
      let lateItemsCount = 0;

      projects.forEach(project => {
        if (project.Subitems && Array.isArray(project.Subitems)) {
          project.Subitems.forEach(subitem => {
            if (subitem.completionDate && subitem.completionDate instanceof Date &&
                subitem.productionDueDate && subitem.productionDueDate instanceof Date) {
              const completionDate = subitem.completionDate;

              // Check if completion date falls within this week
              if (completionDate >= week.startDate && completionDate <= week.endDate) {
                // Calculate days late (completion - due date)
                const timeDiff = completionDate.getTime() - subitem.productionDueDate.getTime();
                const daysLate = Math.ceil(timeDiff / (1000 * 3600 * 24));

                // Only count if actually late (positive days)
                if (daysLate > 0) {
                  totalLateDays += daysLate;
                  lateItemsCount++;
                }
              }
            }
          });
        }
      });

      // Calculate average late days and cap at 25
      const avgLateDays = lateItemsCount > 0 ? Math.round(totalLateDays / lateItemsCount) : 0;
      week.averageLateDays = Math.min(avgLateDays, 25);
      week.actualAverageLateDays = avgLateDays; // Keep original for tooltip
      week.totalLateItems = lateItemsCount;
      week.totalLateDays = totalLateDays;
    });

    return weeks;
  }

  calculateCurrentWeekWIPBreakdown() {
    const projects = this.parentComponent.projects || [];
    const productCounts = {};

    // Get current week date range
    const now = new Date();
    const currentWeekStart = new Date(now);
    currentWeekStart.setDate(now.getDate() - now.getDay()); // Start of current week (Sunday)
    currentWeekStart.setHours(0, 0, 0, 0);

    const currentWeekEnd = new Date(currentWeekStart);
    currentWeekEnd.setDate(currentWeekStart.getDate() + 6); // End of current week (Saturday)
    currentWeekEnd.setHours(23, 59, 59, 999);

    // Count products from active projects and their subitems
    projects.forEach(project => {
      // Only include active/in-progress projects
      if (project.State === 'archived' || project.ShippingStatus === 'Shipped') {
        return;
      }

      if (project.Subitems && Array.isArray(project.Subitems)) {
        project.Subitems.forEach(subitem => {
          // Only count subitems that are not completed or are in current week
          const isInCurrentWeek = subitem.completionDate &&
            subitem.completionDate >= currentWeekStart &&
            subitem.completionDate <= currentWeekEnd;

          const isNotCompleted = !subitem.completionDate ||
            subitem.completionDate > currentWeekEnd;

          if (isInCurrentWeek || isNotCompleted) {
            const product = subitem.product || 'Unknown';
            productCounts[product] = (productCounts[product] || 0) + 1;
          }
        });
      }
    });

    // Convert to array and sort by count (descending)
    return Object.entries(productCounts)
      .map(([product, count]) => ({ product, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 30); // Limit to top 30 products
  }

  render() {
    if (!this.container) return;

    this.container.innerHTML = `
      <div class="space-y-6">
        <div class="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">
          <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-2">Weekly Units Completed</h2>
            <p class="text-gray-600 dark:text-gray-400">Number of units completed each week based on completion dates</p>
          </div>
          <div id="weekly-completion-chart" class="w-full" style="height: 400px;"></div>
        </div>

        <div class="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">
          <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-2">Weekly On-Time Performance</h2>
            <p class="text-gray-600 dark:text-gray-400">Percentage of units completed on time each week (completion date ≤ due date)</p>
          </div>
          <div id="weekly-ontime-chart" class="w-full" style="height: 400px;"></div>
        </div>

        <div class="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">
          <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-2">Average Late Days</h2>
            <p class="text-gray-600 dark:text-gray-400">Average number of days late for completed units each week (completion date - due date)</p>
          </div>
          <div id="weekly-latedays-chart" class="w-full" style="height: 400px;"></div>
        </div>

        <div class="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">
          <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-2">WIP Breakdown</h2>
            <p class="text-gray-600 dark:text-gray-400">Current week work-in-progress breakdown by product type</p>
          </div>
          <div id="wip-breakdown-chart" class="w-full" style="height: 600px;"></div>
        </div>
      </div>
    `;

    this.renderWeeklyChart();
    this.renderOnTimeChart();
    this.renderLateDaysChart();
    this.renderWIPBreakdownChart();
  }

  renderWeeklyChart() {
    const weeklyData = this.calculateWeeklyCompletions();
    const chartDom = document.getElementById('weekly-completion-chart');

    if (this.chart) {
      this.chart.dispose();
    }

    this.chart = echarts.init(chartDom);

    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          return params[0].name + '<br/>' + params[0].value + ' units';
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: weeklyData.map(week => week.label),
        axisLabel: {
          rotate: 45,
          color: '#6B7280',
          fontSize: 12
        },
        name: 'Weeks',
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          color: '#374151',
          fontSize: 14,
          fontWeight: 600
        }
      },
      yAxis: {
        type: 'value',
        name: '# of Units Completed',
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          color: '#374151',
          fontSize: 14,
          fontWeight: 600
        },
        axisLabel: {
          color: '#6B7280'
        },
        min: 0
      },
      series: [{
        name: '# of Units Completed',
        type: 'line',
        data: weeklyData.map(week => week.completedUnits),
        smooth: false,
        lineStyle: {
          color: '#2563EB',
          width: 3
        },
        symbol: 'circle',
        symbolSize: 4,
        itemStyle: {
          color: '#2563EB',
          borderColor: '#2563EB',
          borderWidth: 1
        }
      }]
    };

    this.chart.setOption(option);
  }

  renderOnTimeChart() {
    const weeklyData = this.calculateWeeklyOnTimePerformance();
    const chartDom = document.getElementById('weekly-ontime-chart');

    if (this.onTimeChart) {
      this.onTimeChart.dispose();
    }

    this.onTimeChart = echarts.init(chartDom);

    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          const week = weeklyData[params[0].dataIndex];
          return params[0].name + '<br/>' + params[0].value + '% (' + week.onTimeCompleted + '/' + week.totalCompleted + ' units)';
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: weeklyData.map(week => week.label),
        axisLabel: {
          rotate: 45,
          color: '#6B7280',
          fontSize: 12
        },
        name: 'Weeks',
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          color: '#374151',
          fontSize: 14,
          fontWeight: 600
        }
      },
      yAxis: {
        type: 'value',
        name: 'On-Time Percentage (%)',
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          color: '#374151',
          fontSize: 14,
          fontWeight: 600
        },
        axisLabel: {
          color: '#6B7280',
          formatter: '{value}%'
        },
        min: 0,
        max: 100
      },
      series: [{
        name: 'On-Time Percentage',
        type: 'line',
        data: weeklyData.map(week => week.onTimePercentage),
        smooth: false,
        lineStyle: {
          color: '#2563EB',
          width: 3
        },
        symbol: 'circle',
        symbolSize: 4,
        itemStyle: {
          color: '#2563EB',
          borderColor: '#2563EB',
          borderWidth: 1
        }
      }]
    };

    this.onTimeChart.setOption(option);
  }

  renderLateDaysChart() {
    const weeklyData = this.calculateWeeklyAverageLateDays();
    const chartDom = document.getElementById('weekly-latedays-chart');

    if (this.lateDaysChart) {
      this.lateDaysChart.dispose();
    }

    this.lateDaysChart = echarts.init(chartDom);

    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          const week = weeklyData[params[0].dataIndex];
          if (week.totalLateItems === 0) {
            return params[0].name + '<br/>No late items';
          }
          const actualDays = week.actualAverageLateDays;
          const displayText = actualDays > 25 ? `${actualDays} days average (25+ shown)` : `${actualDays} days average`;
          return params[0].name + '<br/>' + displayText + ' (' + week.totalLateItems + ' late items)';
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: weeklyData.map(week => week.label),
        axisLabel: {
          rotate: 45,
          color: '#6B7280',
          fontSize: 12
        },
        name: 'Weeks',
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          color: '#374151',
          fontSize: 14,
          fontWeight: 600
        }
      },
      yAxis: {
        type: 'value',
        name: 'Average Late Days',
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          color: '#374151',
          fontSize: 14,
          fontWeight: 600
        },
        axisLabel: {
          color: '#6B7280',
          formatter: function(value) {
            return value === 25 ? '25+' : value + ' days';
          }
        },
        min: 0,
        max: 25,
        interval: 5
      },
      series: [{
        name: 'Average Late Days',
        type: 'line',
        data: weeklyData.map(week => week.averageLateDays),
        smooth: false,
        lineStyle: {
          color: '#2563EB',
          width: 3
        },
        symbol: 'circle',
        symbolSize: 4,
        itemStyle: {
          color: '#2563EB',
          borderColor: '#2563EB',
          borderWidth: 1
        }
      }]
    };

    this.lateDaysChart.setOption(option);
  }

  renderWIPBreakdownChart() {
    const wipData = this.calculateCurrentWeekWIPBreakdown();
    const chartDom = document.getElementById('wip-breakdown-chart');

    if (this.wipChart) {
      this.wipChart.dispose();
    }

    this.wipChart = echarts.init(chartDom);

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params) {
          return params[0].name + '<br/>' + params[0].value + ' units';
        }
      },
      grid: {
        left: '15%',
        right: '4%',
        bottom: '3%',
        top: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        name: 'Quantity',
        nameLocation: 'middle',
        nameGap: 30,
        nameTextStyle: {
          color: '#374151',
          fontSize: 12,
          fontWeight: 600
        },
        axisLabel: {
          color: '#6B7280'
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#E5E7EB',
            type: 'dashed'
          }
        }
      },
      yAxis: {
        type: 'category',
        data: wipData.map(item => item.product),
        axisLabel: {
          color: '#6B7280',
          fontSize: 11
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false
        }
      },
      series: [{
        name: 'WIP Quantity',
        type: 'bar',
        data: wipData.map(item => item.count),
        itemStyle: {
          color: '#2563EB'
        },
        barMaxWidth: 20,
        label: {
          show: true,
          position: 'right',
          color: '#374151',
          fontSize: 10
        }
      }]
    };

    this.wipChart.setOption(option);
  }

  async updateData() {
    // Update completion chart
    if (this.chart) {
      const weeklyData = this.calculateWeeklyCompletions();

      this.chart.setOption({
        xAxis: {
          data: weeklyData.map(week => week.label)
        },
        series: [{
          data: weeklyData.map(week => week.completedUnits)
        }]
      });
    }

    // Update on-time performance chart
    if (this.onTimeChart) {
      const onTimeData = this.calculateWeeklyOnTimePerformance();

      this.onTimeChart.setOption({
        xAxis: {
          data: onTimeData.map(week => week.label)
        },
        series: [{
          data: onTimeData.map(week => week.onTimePercentage)
        }]
      });
    }

    // Update average late days chart
    if (this.lateDaysChart) {
      const lateDaysData = this.calculateWeeklyAverageLateDays();

      this.lateDaysChart.setOption({
        xAxis: {
          data: lateDaysData.map(week => week.label)
        },
        series: [{
          data: lateDaysData.map(week => week.averageLateDays)
        }]
      });
    }

    // Update WIP breakdown chart
    if (this.wipChart) {
      const wipData = this.calculateCurrentWeekWIPBreakdown();

      this.wipChart.setOption({
        yAxis: {
          data: wipData.map(item => item.product)
        },
        series: [{
          data: wipData.map(item => item.count)
        }]
      });
    }
  }
} 